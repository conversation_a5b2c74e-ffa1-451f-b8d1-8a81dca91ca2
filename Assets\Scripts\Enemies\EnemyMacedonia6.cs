using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class EnemyMacedonia6 : EnemyMacedonia
{
    [Header("Macedonia 6 Settings")]
    [SerializeField] private float delayFadeOutSeconds = 0.5f;

    // Macedonia 3 variables
    private bool shouldSpawnPomegranateSingles = false;

    // Macedonia 4 variables
    private bool shouldSpawnMandarineSmalls = false;
    private int roundCounter = 0;
    private bool skipDragonFruitThisRound = false;
    private bool spawnedMandarineLastRound = false;

    // Macedonia 5 variables
    private bool isInMelonSliceMode = false;

    // Target indices based on the provided TargetPrefabs list (0-27):
    // 0 - <PERSON>, 1 - <PERSON><PERSON>, 2 - <PERSON>, 3 - <PERSON>, 4 - Coconut
    // 5 - <PERSON>, 6 - Grapes Large, 7 - Grapes Medium, 8 - Grapes Small
    // 9 - <PERSON><PERSON>, 10 - <PERSON>, 11 - <PERSON><PERSON>, 12 - <PERSON><PERSON>, 13 - <PERSON><PERSON>
    // 14 - <PERSON>on, 15 - Nectarine, 16 - <PERSON>, 17 - <PERSON> Slice, 18 - Peach
    // 19 - <PERSON><PERSON>, 20 - Pineapple, 21 - Pomegranate, 22 - Pomegranate Bad
    // 23 - Pomegranate Single, 24 - <PERSON><PERSON><PERSON>, 25 - <PERSON><PERSON><PERSON>, 26 - Watermelon
    // 27 - Watermelon Slice

    protected override void SpawnTargets()
    {
        Weapon weapon = player.GetComponentInChildren<Weapon>();
        Quaternion currentRotation = weapon.transform.rotation;

        // Cache original angles
        float originalOffsetAngle = offsetAngle;
        float originalBufferAngle = bufferAngle;

        activeTargets.Clear();

        List<GameObject> targetsToSpawn = new List<GameObject>();

        // Increment round counter for Macedonia 4 logic
        roundCounter++;

        // === PRIORITY 1: Macedonia 3 - Spawn 8x Pomegranate Single if previous round had Pomegranate Bad ===
        if (shouldSpawnPomegranateSingles)
        {
            offsetAngle = 20f;
            bufferAngle = 70f;

            numberOfTargets = 8;
            for (int i = 0; i < numberOfTargets; i++)
                targetsToSpawn.Add(targetPrefabs[23]); // Pomegranate Single

            shouldSpawnPomegranateSingles = false;
        }
        // === PRIORITY 2: Macedonia 4 - Spawn 6x Mandarine Small if previous round had Mandarine ===
        else if (shouldSpawnMandarineSmalls)
        {
            offsetAngle = 20f;
            bufferAngle = 80f;

            numberOfTargets = 6;
            for (int i = 0; i < numberOfTargets; i++)
                targetsToSpawn.Add(targetPrefabs[12]); // Mandarine Small

            shouldSpawnMandarineSmalls = false;
            skipDragonFruitThisRound = true;
        }
        // === PRIORITY 3: Macedonia 5 - Spawn 4x Watermelon Slice if previous round had Melon destroyed ===
        else if (isInMelonSliceMode)
        {
            numberOfTargets = 4;
            for (int i = 0; i < numberOfTargets; i++)
                targetsToSpawn.Add(targetPrefabs[27]); // Watermelon Slice

            isInMelonSliceMode = false;
        }
        // === PRIORITY 4: Macedonia 4 - Every 3rd round spawn 3x Dragon Fruit ===
        else if (roundCounter % 3 == 0 && !skipDragonFruitThisRound)
        {
            numberOfTargets = 3;
            for (int i = 0; i < numberOfTargets; i++)
                targetsToSpawn.Add(targetPrefabs[5]); // Dragon Fruit
        }
        // === NORMAL SPAWNING: Mix all rules from Macedonia 1-5 ===
        else
        {
            SpawnNormalTargets(targetsToSpawn);
        }

        skipDragonFruitThisRound = false;

        // Spawn the targets
        SpawnTargetObjects(targetsToSpawn, currentRotation, originalOffsetAngle, originalBufferAngle);
    }

    private void SpawnNormalTargets(List<GameObject> targetsToSpawn)
    {
        // Declare flags and counters for all Macedonia rules
        bool hasKiwi = false;
        int pineappleCount = 0;
        bool spawnedPomegranateBad = false;
        int melonCount = 0;

        for (int i = 0; i < numberOfTargets; i++)
        {
            int selectedIndex;
            int attempts = 0;

            do
            {
                selectedIndex = Random.Range(0, targetPrefabs.Count);

                // Macedonia 3 rules: Max 2 pineapples, no pineapple+kiwi combo
                if (selectedIndex == 20 && pineappleCount >= 2) continue; // Pineapple
                if ((selectedIndex == 20 && hasKiwi) || (selectedIndex == 9 && pineappleCount > 0)) continue; // Pineapple+Kiwi conflict

                // Macedonia 4 rules: Exclude Dragon Fruit and Mandarine Small from normal spawning
                if (selectedIndex == 5 || selectedIndex == 12) continue; // Dragon Fruit, Mandarine Small

                // Macedonia 5 rules: Max 2 melons, exclude Watermelon Slice from normal spawning
                if (selectedIndex == 14 && melonCount >= 2) continue; // Melon
                if (selectedIndex == 27) continue; // Watermelon Slice

                break;
            } while (++attempts < 50);

            // Update counters and flags
            if (selectedIndex == 20) pineappleCount++; // Pineapple
            if (selectedIndex == 9) hasKiwi = true; // Kiwi
            if (selectedIndex == 22) spawnedPomegranateBad = true; // Pomegranate Bad
            if (selectedIndex == 14) melonCount++; // Melon
            if (selectedIndex == 11) spawnedMandarineLastRound = true; // Mandarine

            targetsToSpawn.Add(targetPrefabs[selectedIndex]);
        }

        // Macedonia 3 rule: If Kiwi spawned, limit to 2 targets total
        if (hasKiwi)
        {
            targetsToSpawn = targetsToSpawn.GetRange(0, Mathf.Min(2, targetsToSpawn.Count));
            numberOfTargets = 2;
        }

        // Set flags for next round
        if (spawnedPomegranateBad)
        {
            shouldSpawnPomegranateSingles = true;
        }

        if (spawnedMandarineLastRound)
        {
            shouldSpawnMandarineSmalls = true;
        }
    }

    private void SpawnTargetObjects(List<GameObject> targetsToSpawn, Quaternion currentRotation, float originalOffsetAngle, float originalBufferAngle)
    {
        float initialOffset = Utils.CalculateRandomOffset(bufferAngle);
        spawnedMandarineLastRound = false;

        // Check if we're spawning Mandarine Smalls for Macedonia 4 special rotation logic
        bool spawningMandarineSmalls = (targetsToSpawn.Count > 0 && targetsToSpawn[0] == targetPrefabs[12]);

        if (spawningMandarineSmalls)
        {
            // Macedonia 4: Special rotation logic for Mandarine Smalls
            SpawnMandarineSmallsWithSpecialRotation(targetsToSpawn);
        }
        else
        {
            // Normal spawning logic
            for (int i = 0; i < targetsToSpawn.Count; i++)
            {
                GameObject targetObject = Instantiate(targetsToSpawn[i], transform);
                targetObject.transform.position = spawnPoint.transform.position;

                Target target = targetObject.GetComponent<Target>();
                if (target != null)
                {
                    if (i == 0)
                    {
                        target.RotateTo(currentRotation, initialOffset);
                    }
                    else
                    {
                        int sign = initialOffset < 0 ? -1 : 1;
                        target.RotateTo(currentRotation, sign * offsetAngle);
                    }

                    currentRotation = target.transform.rotation;
                    activeTargets.Add(targetObject);

                    // Apply custom behaviors based on target type
                    ApplyCustomTargetBehavior(targetObject, targetsToSpawn[i]);

                    // Macedonia 3: Delay Kiwi animation
                    if (targetsToSpawn[i] == targetPrefabs[9]) // Kiwi
                    {
                        Animator anim = targetObject.GetComponentInChildren<Animator>();
                        if (anim != null)
                        {
                            anim.enabled = false;
                            StartCoroutine(EnableAnimatorAfterDelay(anim, delayFadeOutSeconds));
                        }
                    }

                    // Macedonia 4: Track if Mandarine was spawned
                    if (targetsToSpawn[i] == targetPrefabs[11]) // Mandarine
                    {
                        spawnedMandarineLastRound = true;
                    }
                }
            }
        }

        // Restore original angles
        offsetAngle = originalOffsetAngle;
        bufferAngle = originalBufferAngle;

        // Macedonia 3: Increase target count if no special conditions
        if (!HasKiwiInTargets(targetsToSpawn) && !shouldSpawnPomegranateSingles)
        {
            numberOfTargets = Mathf.Clamp(numberOfTargets + 1, 1, maxNumberOfTargets);
        }

        // Macedonia 4: Set flag for next round if Mandarine was spawned
        if (spawnedMandarineLastRound)
        {
            shouldSpawnMandarineSmalls = true;
        }
    }

    private void SpawnMandarineSmallsWithSpecialRotation(List<GameObject> targetsToSpawn, Quaternion currentRotation)
    {
        // Macedonia 4: Special rotation logic for Mandarine Smalls
        Weapon weapon = player.GetComponentInChildren<Weapon>();
        float startingRotation = weapon.transform.eulerAngles.z;
        int originalFacingDir = weapon.facingDir;

        float forbiddenMin = startingRotation - bufferAngle;
        float forbiddenMax = startingRotation + bufferAngle;

        for (int i = 0; i < targetsToSpawn.Count; i++)
        {
            float targetRotation = startingRotation + originalFacingDir * offsetAngle * i;
            targetRotation = Mathf.Repeat(targetRotation, 360f);

            if (IsInsideBuffer(targetRotation, forbiddenMin, forbiddenMax))
            {
                continue;
            }

            GameObject targetObject = Instantiate(targetsToSpawn[i], transform);
            targetObject.transform.position = spawnPoint.transform.position;

            Target target = targetObject.GetComponent<Target>();
            if (target != null)
            {
                target.RotateTo(targetRotation);
                target.rotationDir = -originalFacingDir;
                activeTargets.Add(targetObject);
            }
        }
    }

    private bool IsInsideBuffer(float rotation, float minBuffer, float maxBuffer)
    {
        // Normalize angles to 0-360 range
        rotation = Mathf.Repeat(rotation, 360f);
        minBuffer = Mathf.Repeat(minBuffer, 360f);
        maxBuffer = Mathf.Repeat(maxBuffer, 360f);

        if (minBuffer <= maxBuffer)
        {
            return rotation >= minBuffer && rotation <= maxBuffer;
        }
        else
        {
            // Buffer wraps around 0/360
            return rotation >= minBuffer || rotation <= maxBuffer;
        }
    }

    private bool HasKiwiInTargets(List<GameObject> targetsToSpawn)
    {
        foreach (GameObject target in targetsToSpawn)
        {
            if (target == targetPrefabs[9]) // Kiwi
                return true;
        }
        return false;
    }

    private void ApplyCustomTargetBehavior(GameObject targetObject, GameObject targetPrefab)
    {
        // Check which target type this is by comparing with prefabs
        for (int i = 0; i < targetPrefabs.Count; i++)
        {
            if (targetPrefabs[i] == targetPrefab)
            {
                // Macedonia 5: Raspberry scaling (index 24)
                if (i == 24) // Raspberry
                {
                    float randomScale = Random.Range(0.2f, 1.0f);

                    // Scale the fruit visual
                    Transform fruit = targetObject.transform.Find("Fruit");
                    if (fruit != null)
                    {
                        fruit.localScale = Vector3.one * randomScale;
                    }

                    // Scale the polygon collider to match
                    PolygonCollider2D polygonCollider = targetObject.GetComponent<PolygonCollider2D>();
                    if (polygonCollider != null)
                    {
                        Vector2[] originalPoints = polygonCollider.points;
                        Vector2[] scaledPoints = new Vector2[originalPoints.Length];

                        for (int j = 0; j < originalPoints.Length; j++)
                        {
                            scaledPoints[j] = originalPoints[j] * randomScale;
                        }

                        polygonCollider.points = scaledPoints;
                    }
                }
                // Macedonia 5: Nectarine vampire mode (index 15)
                else if (i == 15) // Nectarine
                {
                    if (Random.Range(0, 3) == 0) // 1 in 3 chance
                    {
                        TargetNectarine nectarineTarget = targetObject.GetComponent<TargetNectarine>();
                        if (nectarineTarget != null)
                        {
                            nectarineTarget.SetVampireStats();
                        }
                    }
                }
                break;
            }
        }
    }

    private IEnumerator EnableAnimatorAfterDelay(Animator animator, float delay)
    {
        yield return new WaitForSeconds(delay);
        if (animator != null)
        {
            animator.enabled = true;
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        Target.OnTargetDestroyed += CheckForMelonSlice;
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        Target.OnTargetDestroyed -= CheckForMelonSlice;
    }

    private void CheckForMelonSlice(GameObject destroyedTarget)
    {
        // Macedonia 5: Check if the destroyed target was a melon
        if (destroyedTarget != null)
        {
            Target target = destroyedTarget.GetComponent<Target>();
            if (target != null && !target.isRotten)
            {
                // Check if this was a melon by looking at the target type
                if (destroyedTarget.GetComponent<TargetMelon>() != null)
                {
                    isInMelonSliceMode = true;
                }
            }
        }
    }
}
