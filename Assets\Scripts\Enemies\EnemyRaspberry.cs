using UnityEngine;

public class EnemyRaspberry : Enemy
{
    [SerializeField] private float initialScale = 1f;
    [SerializeField] private float scaleDecreasePerWave = 0.05f;
    [SerializeField] private float minScale = 0.2f;
    [SerializeField] private int maxNumberOfTargets = 12;
    [SerializeField] private int scalingStartWave = 5;

    private int waveCount = 0;

    protected override void SpawnTargets()
    {
        Weapon weapon = player.GetComponentInChildren<Weapon>();
        Quaternion currentRotation = weapon.transform.rotation;
        float weaponRotation = Mathf.Repeat(currentRotation.eulerAngles.z, 360f);
        int originalFacingDir = weapon.facingDir;
        float startingRotation = Utils.GetSafeStartingRotation(weaponRotation, bufferAngle, originalFacingDir);

        float forbiddenMin = Mathf.Repeat(weaponRotation - bufferAngle, 360f);
        float forbiddenMax = Mathf.Repeat(weaponRotation + bufferAngle, 360f);

        float targetRotation;

        // Only scale fruit if we've reached the scaling wave
        float currentScale = (waveCount < scalingStartWave)
            ? initialScale
            : Mathf.Max(minScale, initialScale - scaleDecreasePerWave * (waveCount - scalingStartWave));

        for (int i = 0; i < numberOfTargets; i++)
        {
            targetRotation = startingRotation + originalFacingDir * offsetAngle * i;
            targetRotation = Mathf.Repeat(targetRotation, 360f);

            if (IsInsideBuffer(targetRotation, forbiddenMin, forbiddenMax))
            {
                continue;
            }

            GameObject targetObject = Instantiate(targetPrefabs[0], transform);
            targetObject.transform.position = spawnPoint.transform.position;

            Target target = targetObject.GetComponent<Target>();
            if (target != null)
            {
                target.RotateTo(targetRotation);
                target.rotationDir = -originalFacingDir;

                // Scale the fruit visual
                Transform fruit = target.transform.Find("Fruit");
                if (fruit != null)
                {
                    fruit.localScale = Vector3.one * currentScale;
                }

                // Scale the polygon collider to match
                PolygonCollider2D polygonCollider = targetObject.GetComponent<PolygonCollider2D>();
                if (polygonCollider != null)
                {
                    Vector2[] originalPoints = polygonCollider.points;
                    Vector2[] scaledPoints = new Vector2[originalPoints.Length];

                    for (int j = 0; j < originalPoints.Length; j++)
                    {
                        scaledPoints[j] = originalPoints[j] * currentScale;
                    }

                    polygonCollider.points = scaledPoints;
                }

                activeTargets.Add(targetObject);
            }
        }

        // Only increase number of targets after the scaling start wave
        if (waveCount >= scalingStartWave && numberOfTargets < maxNumberOfTargets)
        {
            numberOfTargets++;
        }

        waveCount++;
    }

    private bool IsInsideBuffer(float angle, float forbiddenMin, float forbiddenMax)
    {
        if (forbiddenMin < forbiddenMax)
        {
            return angle >= forbiddenMin && angle <= forbiddenMax;
        }
        else
        {
            return angle >= forbiddenMin || angle <= forbiddenMax;
        }
    }
}
